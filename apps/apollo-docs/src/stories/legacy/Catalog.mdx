import React from "react"
import { createTheme, ThemeProvider } from "@apollo/ui/legacy"
import { Meta } from "@storybook/addon-docs/blocks"
import Catalog from "../../components/catalog/Catalog"
import legacyComponents from "../../components/catalog/legacyComponents"

import "../../app/tailwind.css"

<Meta title="@design-systems∕apollo-ui/Catalog" tags={["docs"]} />

# Catalog

<ThemeProvider theme={createTheme()}>

  <Catalog components={legacyComponents} />
</ThemeProvider>
