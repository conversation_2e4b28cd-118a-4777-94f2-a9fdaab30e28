import React from "react"
import { Typo<PERSON>, createTheme, ThemeProvider } from "@apollo/ui/legacy"
import { Meta } from "@storybook/addon-docs/blocks"
import Catalog from "../../components/catalog/Catalog"
import legacyComponents from "../../components/catalog/legacyComponents"

<Meta title="@design-systems∕apollo-ui/Catalog" tags={["docs"]} />

<ThemeProvider theme={createTheme()}>
  <Catalog components={legacyComponents} />
</ThemeProvider>
