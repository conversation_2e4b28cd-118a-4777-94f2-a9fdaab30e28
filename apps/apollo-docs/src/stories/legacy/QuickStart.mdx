import React, { useState } from "react"
import { Button, Input, Typography, createTheme, ThemeProvider } from "@apollo/ui/legacy"
import { Meta } from "@storybook/addon-docs/blocks"

import CautionCard from "../../components/caution-card/CautionCard"

<Meta title="@design-systems∕apollo-ui/Quick Start" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="h1"
    style={{ margin: "0px", color: "#121212" }}
  >Quick Start</Typography>
  <Typography
    align="center"
    level="h4"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Get started with the legacy Apollo Design System components!</Typography>
</div>

<CautionCard>
This is the legacy version of Apollo Design System. For new projects, consider using <code>@apollo/ui</code> instead.
</CautionCard>

## Installation

To install the `@design-systems/apollo-ui` package, you need to configure the `.npmrc` file (create this file in the project root) to point to our custom private registry on GitLab.

```bash title=".npmrc"
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
```

<CautionCard>
If you're outside CJ network, VPN is needed to install this package.
</CautionCard>

```bash
# with pnpm (recommended)
pnpm add @design-systems/apollo-ui

# with yarn
yarn add @design-systems/apollo-ui

# with npm
npm install @design-systems/apollo-ui
```

## Tailwind Configuration

Apollo UI legacy components are built with TailwindCSS. You need to configure Tailwind to include the Apollo preset:

```js title="tailwind.config.js"
const { withApollo } = require("@design-systems/apollo-ui")

module.exports = withApollo({
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    // Add other content paths as needed
  ],
  // Any additional Tailwind configuration
})
```

## Theme Setup

Apollo legacy components require a theme provider to work correctly. Wrap your app with the `ThemeProvider` and create a theme:

```jsx title="App.js"
import React from 'react';
import { ThemeProvider, createTheme } from "@design-systems/apollo-ui"

const appTheme = createTheme()

function App({ children }) {
  return (
    <ThemeProvider theme={appTheme}>
      {children}
    </ThemeProvider>
  )
}
```

## Basic Usage

You can import and use components directly:

```jsx title="Button Example"
import React from 'react'
import { Button, ThemeProvider, createTheme } from "@design-systems/apollo-ui"

const theme = createTheme()

export default function Example() {
  return (
    <ThemeProvider theme={theme}>
      <Button variant="filled">Hello World</Button>
    </ThemeProvider>
  )
}
```