import React from "react"
import { Typography } from "@apollo/ui"
import { Meta } from "@storybook/addon-docs/blocks"

import CautionCard from "../../components/caution-card/CautionCard"

<Meta title="@apollo∕ui/Quick Start" tags={["docs"]} />

<div
  style={{
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    textAlign: "center",
    gap: 16,
    padding: "16px 0 32px",
  }}
>
  <Typography
    align="center"
    level="displayMedium"
    style={{ margin: "0px", color: "#121212" }}
  >Quick Start</Typography>
  <Typography
    align="center"
    level="titleLarge"
    style={{ color: "var(--sb-secondary-text-color)" }}
  >Install the library with ease and start building amazing UIs!</Typography>
</div>


Currently our Apollo Design System is still being a private design system used internally at our company.

To install the `@apollo/ui` package, we need to configure the `.npmrc` file (create this file in the project root) to point to our custom private registry on GitLab.

```bash title=".npmrc"
@design-systems:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
@apollo:registry=https://gitlab.cjexpress.io/api/v4/projects/1875/packages/npm/
```

## Install

<CautionCard>
If you're outside CJ network, VPN is needed to install this package.
</CautionCard>


```bash
# with pnpm (recommended)
pnpm add @apollo/ui

# with yarn
yarn add @apollo/ui

# with npm
npm install @apollo/ui
```

## Setup

Apollo components are styled using CSS variables. To ensure consistent styling, you need to use the `<ApolloProvider />` from the `@apollo/ui` package.

Place a `<ApolloProvider />` at the root of your app and pass theme as a prop.

```jsx title="App.js"
import React from 'react';
import { ApolloProvider, createThemeV2 } from "@apollo/ui"

const appTheme = createThemeV2()

function App({ children }) {
  return <ApolloProvider themeProps={{ theme: appTheme }}>{children}</ApolloProvider>
}
```

## Usage

You can import and use components directly:

```jsx title="Button Example"
import React from 'react'
import { Button } from "@apollo/ui"

export default function Example() {
  return <Button>Hello World</Button>
}
```

## Theming

Apollo UI supports flexible theming through design tokens. You can customize the appearance by creating custom themes:

```jsx title="Custom Theme"
import { createThemeV2 } from "@apollo/ui"

const customTheme = createThemeV2({
  tokens: {
    base: {
      color: {
        primary: {
          40: "#007bff",
        }
      }
    }
  }
})

function App() {
  return (
    <ApolloProvider themeProps={{ theme: customTheme }}>
      {/* Your app content */}
    </ApolloProvider>
  )
}
```

## TypeScript Support

Apollo UI is built with TypeScript and provides excellent type safety out of the box:

```tsx title="TypeScript Example"
import React from 'react'
import { Button, ButtonProps } from "@apollo/ui"

interface CustomButtonProps extends ButtonProps {
  loading?: boolean
}

const CustomButton: React.FC<CustomButtonProps> = ({ 
  loading, 
  children, 
  ...props 
}) => {
  return (
    <Button {...props} disabled={loading}>
      {loading ? 'Loading...' : children}
    </Button>
  )
}
```