import React, { useMemo, useState } from "react"
import { Input, Typography } from "@apollo/ui"
import { Search } from "@design-systems/apollo-icons"

import StorybookLink from "../storybook-link/StorybookLink"

interface ComponentItem {
  title: string
  href: string
  description: string
  keywords: string[]
  component: React.ReactNode
}

interface CatalogProps {
  components?: ComponentItem[]
}

const Catalog: React.FC<CatalogProps> = ({ components }) => {
  const [searchTerm, setSearchTerm] = useState("")

  const filteredComponents = useMemo(() => {
    if (!components) return []
    if (!searchTerm) return components

    return components.filter((item) => {
      const searchLower = searchTerm.toLowerCase()
      return (
        item.title.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower) ||
        item.keywords.some((keyword) =>
          keyword.toLowerCase().includes(searchLower)
        )
      )
    })
  }, [searchTerm, components])

  return (
    <div
      style={{
        margin: "0 auto",
        padding: "24px",
        border: "1px solid #e9ecef",
        borderRadius: "12px",
      }}
    >
      {/* Search Section */}
      <div
        style={{
          marginBottom: "40px",
          width: "100%",
        }}
      >
        <Typography
          level="titleLarge"
          style={{
            marginBottom: "16px",
            color: "#121212",
            fontWeight: 600,
          }}
        >
          Search Components
        </Typography>
        <Input
          placeholder="Search by component name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          startDecorator={<Search size={20} />}
        />
      </div>

      {/* Results Section */}
      {filteredComponents.length === 0 ? (
        <div
          style={{
            textAlign: "center",
            padding: "64px 24px",
            backgroundColor: "#f8f9fa",
            borderRadius: "12px",
            border: "1px solid #e9ecef",
          }}
        >
          <Typography
            level="headlineSmall"
            style={{ color: "#6c757d", marginBottom: "8px" }}
          >
            No components found
          </Typography>
          <Typography
            level="bodyLarge"
            style={{ color: "#6c757d", marginBottom: "16px" }}
          >
            No components match "{searchTerm}"
          </Typography>
          <Typography level="bodyMedium" style={{ color: "#adb5bd" }}>
            Try searching for "product", "card", "price", "badge", or "tab"
          </Typography>
        </div>
      ) : (
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gridAutoRows: "1fr",
            gap: "24px",
          }}
        >
          {filteredComponents.map((item, index) => (
            <div
              key={`${item.title}-${index}`}
              style={{
                alignSelf: "stretch",
                display: "flex",
                flexDirection: "column",
                gap: "16px",
              }}
            >
              {/* Component Preview */}
              {item.component && (
                <div
                  style={{
                    border: "1px solid #e9ecef",
                    borderRadius: "8px",
                    padding: "20px",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    minHeight: "120px",
                    height: "100%",
                  }}
                >
                  {item.component}
                </div>
              )}
              {/* Component Header */}
              <div>
                <StorybookLink page={item.href}>
                  <Typography level="titleMedium" style={{ fontWeight: 700 }}>
                    {item.title}
                  </Typography>
                </StorybookLink>
                <Typography
                  level="bodyMedium"
                  style={{
                    color: "#6c757d",
                    lineHeight: 1.4,
                  }}
                >
                  {item.description}
                </Typography>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default Catalog
