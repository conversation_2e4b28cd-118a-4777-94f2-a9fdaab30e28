import {
  Accordion,
  Alert,
  Autocomplete,
  Breadcrumbs,
  Button,
  CapsuleTab,
  Checkbox,
  Chip,
  DateInput,
  FloatButton,
  IconButton,
  Input,
  Modal,
  Pagination,
  ProductCard,
  Radio,
  RadioGroup,
  Select,
  SortingIcon,
  Switch,
  Tab,
  TabPanel,
  Tabs,
  TabsList,
  ThemeProvider,
  Toast,
  Typography,
  UploadBox,
  createTheme,
} from "@apollo/ui/legacy"
import {
  Search,
  Heart,
  Setting,
  Plus,
  Home,
  User,
  Bell,
  Star,
} from "@design-systems/apollo-icons"

const legacyComponents = [
  // INPUTS CATEGORY
  {
    title: "Button",
    href: "@design-systems∕apollo-ui/Components/Inputs/Button",
    description: "Primary action component with multiple variants and sizes",
    keywords: ["button", "action", "click", "primary", "negative", "filled", "outline", "text"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Button variant="filled">Filled</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="text">Text</Button>
        </div>
      </ThemeProvider>
    ),
  },
  {
    title: "Input",
    href: "@design-systems∕apollo-ui/Components/Inputs/Input",
    description: "Text input field with validation and helper text support",
    keywords: ["input", "text", "field", "form", "validation"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <Input placeholder="Enter text..." style={{ width: "200px" }} />
      </ThemeProvider>
    ),
  },
  {
    title: "Checkbox",
    href: "@design-systems∕apollo-ui/Components/Inputs/Checkbox",
    description: "Binary choice input with checked, unchecked, and indeterminate states",
    keywords: ["checkbox", "check", "selection", "binary", "form"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
          <Checkbox checked />
          <Checkbox />
          <Checkbox indeterminate />
        </div>
      </ThemeProvider>
    ),
  },
  {
    title: "Radio",
    href: "@design-systems∕apollo-ui/Components/Inputs/Radio",
    description: "Single choice selection from a group of options",
    keywords: ["radio", "selection", "choice", "group", "form"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <RadioGroup value="option1">
          <Radio value="option1" label="Option 1" />
          <Radio value="option2" label="Option 2" />
        </RadioGroup>
      </ThemeProvider>
    ),
  },
  {
    title: "Select",
    href: "@design-systems∕apollo-ui/Components/Inputs/Select",
    description: "Dropdown selection component with search and multi-select support",
    keywords: ["select", "dropdown", "options", "search", "multi-select"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <Select
          placeholder="Choose option..."
          options={[
            { label: "Option 1", value: "1" },
            { label: "Option 2", value: "2" },
          ]}
          style={{ width: "200px" }}
        />
      </ThemeProvider>
    ),
  },
  {
    title: "Switch",
    href: "@design-systems∕apollo-ui/Components/Inputs/Switch",
    description: "Toggle switch for binary settings and preferences",
    keywords: ["switch", "toggle", "binary", "settings", "on", "off"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
          <Switch checked />
          <Switch />
        </div>
      </ThemeProvider>
    ),
  },
  {
    title: "Date Input",
    href: "@design-systems∕apollo-ui/Components/Inputs/DateInput",
    description: "Date picker input with calendar interface",
    keywords: ["date", "picker", "calendar", "input", "time"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <DateInput placeholder="Select date..." style={{ width: "200px" }} />
      </ThemeProvider>
    ),
  },
  {
    title: "Autocomplete",
    href: "@design-systems∕apollo-ui/Components/Inputs/Autocomplete",
    description: "Input with autocomplete suggestions and filtering",
    keywords: ["autocomplete", "search", "suggestions", "filter", "input"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <Autocomplete
          placeholder="Search..."
          options={[
            { label: "Apple", value: "apple" },
            { label: "Banana", value: "banana" },
          ]}
          style={{ width: "200px" }}
        />
      </ThemeProvider>
    ),
  },
  {
    title: "Upload Box",
    href: "@design-systems∕apollo-ui/Components/Inputs/UploadBox",
    description: "File upload component with drag and drop support",
    keywords: ["upload", "file", "drag", "drop", "attachment"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <UploadBox style={{ width: "200px", height: "120px" }} />
      </ThemeProvider>
    ),
  },

  // DATA DISPLAY CATEGORY
  {
    title: "Typography",
    href: "@design-systems∕apollo-ui/Components/Data Display/Typography",
    description: "Text component with semantic levels and styling options",
    keywords: ["typography", "text", "heading", "body", "semantic"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <div style={{ display: "flex", flexDirection: "column", gap: "4px" }}>
          <Typography level="h3">Heading</Typography>
          <Typography level="body-1">Body text</Typography>
        </div>
      </ThemeProvider>
    ),
  },
  {
    title: "Alert",
    href: "@design-systems∕apollo-ui/Components/Data Display/Alert",
    description: "Notification component for important messages",
    keywords: ["alert", "notification", "message", "warning", "error", "success"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <Alert severity="info" style={{ width: "200px" }}>
          Information alert
        </Alert>
      </ThemeProvider>
    ),
  },
  {
    title: "Chip",
    href: "@design-systems∕apollo-ui/Components/Data Display/Chip",
    description: "Compact element for tags, filters, or selections",
    keywords: ["chip", "tag", "filter", "selection", "removable"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip label="Tag 1" />
          <Chip label="Removable" onClose={() => {}} />
        </div>
      </ThemeProvider>
    ),
  },
  {
    title: "Sorting Icon",
    href: "@design-systems∕apollo-ui/Components/Data Display/SortingIcon",
    description: "Icon indicating sort direction in tables",
    keywords: ["sorting", "icon", "table", "direction", "asc", "desc"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
          <SortingIcon status="asc" />
          <SortingIcon status="desc" />
          <SortingIcon />
        </div>
      </ThemeProvider>
    ),
  },

  // NAVIGATION CATEGORY
  {
    title: "Breadcrumbs",
    href: "@design-systems∕apollo-ui/Components/Navigation/Breadcrumbs",
    description: "Navigation trail showing current page location",
    keywords: ["breadcrumbs", "navigation", "trail", "path", "hierarchy"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <Breadcrumbs>
          <Typography level="body-1">Home</Typography>
          <Typography level="body-1">Category</Typography>
          <Typography level="body-1">Current</Typography>
        </Breadcrumbs>
      </ThemeProvider>
    ),
  },
  {
    title: "Pagination",
    href: "@design-systems∕apollo-ui/Components/Navigation/Pagination",
    description: "Navigation control for paginated content",
    keywords: ["pagination", "navigation", "pages", "next", "previous"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <Pagination
          count={5}
          defaultPage={2}
          onChange={() => {}}
        />
      </ThemeProvider>
    ),
  },
  {
    title: "Tabs",
    href: "@design-systems∕apollo-ui/Components/Navigation/Tabs",
    description: "Tabbed interface for organizing content sections",
    keywords: ["tabs", "navigation", "sections", "content", "switch"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <Tabs defaultValue={0} style={{ width: "200px" }}>
          <TabsList>
            <Tab>Tab 1</Tab>
            <Tab>Tab 2</Tab>
          </TabsList>
          <TabPanel value={0}>Content 1</TabPanel>
          <TabPanel value={1}>Content 2</TabPanel>
        </Tabs>
      </ThemeProvider>
    ),
  },
  {
    title: "Capsule Tab",
    href: "@design-systems∕apollo-ui/Components/Navigation/CapsuleTab",
    description: "Pill-style tab component for compact navigation",
    keywords: ["capsule", "tab", "pill", "navigation", "compact"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <div style={{ display: "flex", gap: "8px" }}>
          <CapsuleTab active>Active</CapsuleTab>
          <CapsuleTab>Inactive</CapsuleTab>
        </div>
      </ThemeProvider>
    ),
  },

  // FEEDBACK CATEGORY
  {
    title: "Modal",
    href: "@design-systems∕apollo-ui/Components/Feedback/Modal",
    description: "Dialog overlay for important content and actions",
    keywords: ["modal", "dialog", "overlay", "popup", "confirmation"],
    component: null, // Modal requires complex state management for demo
  },
  {
    title: "Toast",
    href: "@design-systems∕apollo-ui/Components/Feedback/Toast",
    description: "Temporary notification messages",
    keywords: ["toast", "notification", "message", "temporary", "snackbar"],
    component: null, // Toast requires provider setup for demo
  },

  // LAYOUT CATEGORY
  {
    title: "Accordion",
    href: "@design-systems∕apollo-ui/Components/Layout/Accordion",
    description: "Collapsible content sections",
    keywords: ["accordion", "collapsible", "expand", "collapse", "sections"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <Accordion label="Section 1" style={{ width: "200px" }}>
          <Typography level="body-1">Content for section 1</Typography>
        </Accordion>
      </ThemeProvider>
    ),
  },

  // UTILITIES CATEGORY
  {
    title: "Icon Button",
    href: "@design-systems∕apollo-ui/Components/Utilities/IconButton",
    description: "Button component specifically for icons",
    keywords: ["icon", "button", "action", "circular", "square"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
          <IconButton>
            <Heart size={20} />
          </IconButton>
          <IconButton>
            <Setting size={20} />
          </IconButton>
        </div>
      </ThemeProvider>
    ),
  },
  {
    title: "Float Button",
    href: "@design-systems∕apollo-ui/Components/Utilities/FloatButton",
    description: "Floating action button for primary actions",
    keywords: ["float", "button", "fab", "floating", "action", "primary"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <FloatButton>
          <Plus size={20} />
        </FloatButton>
      </ThemeProvider>
    ),
  },
  {
    title: "Product Card",
    href: "@design-systems∕apollo-ui/Components/Utilities/ProductCard",
    description: "Card component for displaying product information",
    keywords: ["product", "card", "display", "image", "title", "pricing"],
    component: (
      <ThemeProvider theme={createTheme()}>
        <ProductCard
          title="Sample Product"
          imageSrc="https://picsum.photos/150/150?random=1"
          style={{ width: "150px" }}
        />
      </ThemeProvider>
    ),
  },
]

export default legacyComponents
